"""
Comprehensive form tests for the campaigns app.

This module tests all form functionality including:
- Form validation and field constraints
- Custom form methods and clean methods
- Form initialization and field population
- Error handling and user feedback
- Integration with models and relationships
"""

import json
import uuid
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User

from campaigns.forms import (
    CampaignForm, CampaignTagForm, DynamicTagForm,
    LocationTargetForm, UsernameTargetForm, BulkUsernameForm
)
from campaigns.models import (
    Campaign, DynamicTag, TagGroup, TagCategory, CampaignTag,
    LocationTarget, UsernameTarget
)
from campaigns.tests.base import BaseTestCase
from campaigns.tests.factories import (
    CampaignFactory, DynamicTagFactory, TagGroupFactory, TagCategoryFactory,
    UserFactory, CampaignTagFactory
)


class CampaignFormTest(BaseTestCase):
    """Test the CampaignForm."""
    
    def test_campaign_form_valid_data(self):
        """Test campaign form with valid data."""
        form_data = {
            'name': 'Test Campaign',
            'description': 'Test campaign description',
            'target_type': 'location',
            'audience_type': 'followers',
            'enable_location_targeting': True,
            'enable_username_targeting': False,
            'location_targets': ['location_123'],
            'usernames': ''
        }
        
        form = CampaignForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_campaign_form_required_fields(self):
        """Test campaign form required field validation."""
        form_data = {}
        form = CampaignForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
    
    def test_campaign_form_name_max_length(self):
        """Test campaign form name field max length validation."""
        form_data = {
            'name': 'x' * 121,  # Exceeds max_length of 120
            'description': 'Test description',
            'target_type': 'location',
            'audience_type': 'followers'
        }
        
        form = CampaignForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
    
    def test_campaign_form_target_type_choices(self):
        """Test campaign form target type field choices validation."""
        valid_choices = ['location', 'username', 'mixed']
        
        for choice in valid_choices:
            form_data = {
                'name': 'Test Campaign',
                'target_type': choice,
                'audience_type': 'followers'
            }
            form = CampaignForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for target_type: {choice}")
        
        # Test invalid choice
        form_data = {
            'name': 'Test Campaign',
            'target_type': 'invalid_choice',
            'audience_type': 'followers'
        }
        form = CampaignForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('target_type', form.errors)
    
    def test_campaign_form_audience_type_choices(self):
        """Test campaign form audience type field choices validation."""
        valid_choices = ['profile', 'followers', 'following', 'both']
        
        for choice in valid_choices:
            form_data = {
                'name': 'Test Campaign',
                'target_type': 'location',
                'audience_type': choice
            }
            form = CampaignForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for audience_type: {choice}")
        
        # Test invalid choice
        form_data = {
            'name': 'Test Campaign',
            'target_type': 'location',
            'audience_type': 'invalid_choice'
        }
        form = CampaignForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('audience_type', form.errors)
    
    def test_campaign_form_username_json_validation(self):
        """Test campaign form username JSON validation."""
        # Test valid JSON format
        form_data = {
            'name': 'Test Campaign',
            'target_type': 'username',
            'audience_type': 'followers',
            'usernames': json.dumps([
                {'username': 'user1', 'audienceType': 'followers'},
                {'username': 'user2', 'audienceType': 'following'}
            ])
        }
        form = CampaignForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
        
        # Test newline-separated format (backward compatibility)
        form_data['usernames'] = 'user1\nuser2\nuser3'
        form = CampaignForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_campaign_form_save_method(self):
        """Test campaign form save method."""
        form_data = {
            'name': 'Test Campaign Save',
            'description': 'Test description',
            'target_type': 'location',
            'audience_type': 'followers',
            'location_targets': ['location_123'],
            'usernames': ''
        }
        
        form = CampaignForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        campaign = form.save()
        self.assertIsInstance(campaign, Campaign)
        self.assertEqual(campaign.name, 'Test Campaign Save')
        self.assertEqual(campaign.description, 'Test description')


class CampaignTagFormTest(BaseTestCase):
    """Test the CampaignTagForm - specifically testing the recent tag_groups fix."""
    
    def test_campaign_tag_form_initialization(self):
        """Test campaign tag form initialization with campaign parameter."""
        campaign = CampaignFactory.create()
        form = CampaignTagForm(campaign=campaign)
        
        # Form should initialize without errors
        self.assertIsNotNone(form.fields['tag'].queryset)
    
    def test_campaign_tag_form_tag_queryset_filtering(self):
        """Test that form correctly filters tags using tag_groups field (not tag_group)."""
        campaign = CampaignFactory.create()
        
        # Create a global tag group
        global_group = TagGroupFactory.create(is_global=True)
        
        # Create tags with different configurations
        global_tag = DynamicTagFactory.create(is_global=True, tag_groups=[global_group])
        non_global_tag = DynamicTagFactory.create(is_global=False, tag_groups=[])
        group_global_tag = DynamicTagFactory.create(is_global=False, tag_groups=[global_group])
        
        # Initialize form
        form = CampaignTagForm(campaign=campaign)
        
        # Check that queryset includes global tags and tags in global groups
        queryset_ids = list(form.fields['tag'].queryset.values_list('id', flat=True))
        
        self.assertIn(global_tag.id, queryset_ids)
        self.assertIn(group_global_tag.id, queryset_ids)
        self.assertNotIn(non_global_tag.id, queryset_ids)
    
    def test_campaign_tag_form_excludes_assigned_tags(self):
        """Test that form excludes already assigned tags."""
        campaign = CampaignFactory.create()
        tag1 = DynamicTagFactory.create(is_global=True)
        tag2 = DynamicTagFactory.create(is_global=True)
        
        # Assign tag1 to campaign
        CampaignTagFactory.create(campaign=campaign, tag=tag1)
        
        # Initialize form
        form = CampaignTagForm(campaign=campaign)
        
        # Check that queryset excludes assigned tag
        queryset_ids = list(form.fields['tag'].queryset.values_list('id', flat=True))
        
        self.assertNotIn(tag1.id, queryset_ids)
        self.assertIn(tag2.id, queryset_ids)
    
    def test_campaign_tag_form_update_includes_current_tag(self):
        """Test that form includes current tag when updating existing CampaignTag."""
        campaign = CampaignFactory.create()
        tag1 = DynamicTagFactory.create(is_global=True)
        tag2 = DynamicTagFactory.create(is_global=True)
        
        # Create campaign tag
        campaign_tag = CampaignTagFactory.create(campaign=campaign, tag=tag1)
        
        # Initialize form for updating
        form = CampaignTagForm(campaign=campaign, instance=campaign_tag)
        
        # Check that queryset includes current tag
        queryset_ids = list(form.fields['tag'].queryset.values_list('id', flat=True))
        
        self.assertIn(tag1.id, queryset_ids)  # Current tag should be included
        self.assertIn(tag2.id, queryset_ids)  # Other available tags should be included
    
    def test_campaign_tag_form_valid_data(self):
        """Test campaign tag form with valid data."""
        campaign = CampaignFactory.create()
        tag = DynamicTagFactory.create(is_global=True)
        
        form_data = {
            'tag': tag.id,
            'is_required': True
        }
        
        form = CampaignTagForm(data=form_data, campaign=campaign)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_campaign_tag_form_without_campaign_parameter(self):
        """Test campaign tag form without campaign parameter."""
        tag = DynamicTagFactory.create(is_global=True)
        
        form_data = {
            'tag': tag.id,
            'is_required': False
        }
        
        form = CampaignTagForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
        
        # Should include all global tags
        queryset_ids = list(form.fields['tag'].queryset.values_list('id', flat=True))
        self.assertIn(tag.id, queryset_ids)


class DynamicTagFormTest(BaseTestCase):
    """Test the DynamicTagForm."""
    
    def test_dynamic_tag_form_valid_data(self):
        """Test dynamic tag form with valid data."""
        category = TagCategoryFactory.create()
        
        form_data = {
            'name': 'Test Tag',
            'description': 'Test tag description',
            'category': category.id,
            'tag_type': 'keyword',
            'pattern': 'test',
            'field': 'bio',
            'is_global': True,
            'weight': 1.5
        }
        
        form = DynamicTagForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_dynamic_tag_form_required_fields(self):
        """Test dynamic tag form required field validation."""
        form_data = {}
        form = DynamicTagForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertIn('pattern', form.errors)
        self.assertIn('field', form.errors)
    
    def test_dynamic_tag_form_tag_type_choices(self):
        """Test dynamic tag form tag type field choices validation."""
        valid_choices = ['keyword', 'regex', 'sentiment', 'category', 'ml', 'nlp']
        
        for choice in valid_choices:
            form_data = {
                'name': 'Test Tag',
                'pattern': 'test',
                'field': 'bio',
                'tag_type': choice
            }
            form = DynamicTagForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for tag_type: {choice}")
    
    def test_dynamic_tag_form_weight_validation(self):
        """Test dynamic tag form weight field validation."""
        # Test valid weight
        form_data = {
            'name': 'Test Tag',
            'pattern': 'test',
            'field': 'bio',
            'weight': 2.5
        }
        form = DynamicTagForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
        
        # Test negative weight (should be valid as no constraint specified)
        form_data['weight'] = -1.0
        form = DynamicTagForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_dynamic_tag_form_save_method(self):
        """Test dynamic tag form save method."""
        category = TagCategoryFactory.create()
        
        form_data = {
            'name': 'Test Tag Save',
            'description': 'Test description',
            'category': category.id,
            'tag_type': 'keyword',
            'pattern': 'test',
            'field': 'bio',
            'is_global': True,
            'weight': 1.0
        }
        
        form = DynamicTagForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        tag = form.save()
        self.assertIsInstance(tag, DynamicTag)
        self.assertEqual(tag.name, 'Test Tag Save')
        self.assertEqual(tag.category, category)


class FormFieldValidationTest(BaseTestCase):
    """Test form field validation and error handling."""
    
    def test_form_field_max_length_validation(self):
        """Test form field max length validation across different forms."""
        # Test campaign name max length
        form_data = {'name': 'x' * 121}  # Exceeds Campaign.name max_length
        form = CampaignForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        
        # Test tag name max length
        form_data = {'name': 'x' * 101, 'pattern': 'test', 'field': 'bio'}  # Exceeds DynamicTag.name max_length
        form = DynamicTagForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
    
    def test_form_choice_field_validation(self):
        """Test choice field validation across different forms."""
        # Test invalid campaign target type
        form_data = {
            'name': 'Test',
            'target_type': 'invalid',
            'audience_type': 'followers'
        }
        form = CampaignForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('target_type', form.errors)
        
        # Test invalid tag type
        form_data = {
            'name': 'Test Tag',
            'pattern': 'test',
            'field': 'bio',
            'tag_type': 'invalid'
        }
        form = DynamicTagForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('tag_type', form.errors)
