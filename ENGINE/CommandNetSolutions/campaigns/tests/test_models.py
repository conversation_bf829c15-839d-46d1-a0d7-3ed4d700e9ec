"""
Comprehensive model tests for the campaigns app.

This module tests all model functionality including:
- Model creation and validation
- Field constraints and validation
- Relationships and foreign keys
- Custom model methods
- Model managers and querysets
- Cascade deletion behavior
"""

import uuid
import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.utils import timezone
from django.contrib.auth.models import User

from campaigns.models import (
    Campaign, DynamicTag, TagGroup, TagCategory, CampaignTag,
    LocationTarget, UsernameTarget, WorkflowExecution, WorkflowProgressUpdate,
    TagAnalysisResult, CampaignResult, TagMetrics, CampaignTagRule
)
from campaigns.tests.base import BaseTestCase
from campaigns.tests.factories import (
    CampaignFactory, DynamicTagFactory, TagGroupFactory, TagCategoryFactory,
    UserFactory, CampaignTagFactory, LocationTargetFactory, UsernameTargetFactory
)


class CampaignModelTest(BaseTestCase):
    """Test the Campaign model."""

    def test_campaign_creation(self):
        """Test basic campaign creation."""
        campaign = CampaignFactory.create(
            name='Test Campaign',
            description='Test description',
            status='draft'
        )

        self.assertEqual(campaign.name, 'Test Campaign')
        self.assertEqual(campaign.description, 'Test description')
        self.assertEqual(campaign.status, 'draft')
        self.assertIsNotNone(campaign.id)
        self.assertIsInstance(campaign.id, uuid.UUID)
        self.assertIsNotNone(campaign.created_at)
        self.assertIsNotNone(campaign.updated_at)

    def test_campaign_str_representation(self):
        """Test campaign string representation."""
        campaign = CampaignFactory.create(name='My Test Campaign')
        self.assertEqual(str(campaign), 'My Test Campaign')

    def test_campaign_status_choices(self):
        """Test campaign status field choices."""
        valid_statuses = ['draft', 'pending', 'running', 'paused', 'completed', 'failed', 'stopped']

        for status in valid_statuses:
            campaign = CampaignFactory.create(status=status)
            self.assertEqual(campaign.status, status)

    def test_campaign_target_type_choices(self):
        """Test campaign target type field choices."""
        valid_types = ['location', 'username', 'mixed']

        for target_type in valid_types:
            campaign = CampaignFactory.create(target_type=target_type)
            self.assertEqual(campaign.target_type, target_type)

    def test_campaign_audience_type_choices(self):
        """Test campaign audience type field choices."""
        valid_types = ['profile', 'followers', 'following', 'both']

        for audience_type in valid_types:
            campaign = CampaignFactory.create(audience_type=audience_type)
            self.assertEqual(campaign.audience_type, audience_type)

    def test_campaign_creator_relationship(self):
        """Test campaign creator foreign key relationship."""
        user = UserFactory.create()
        campaign = CampaignFactory.create(creator=user)

        self.assertEqual(campaign.creator, user)
        self.assertIn(campaign, user.campaign_set.all())

    def test_campaign_dmp_conf_default(self):
        """Test campaign dmp_conf field default value."""
        campaign = CampaignFactory.create()
        self.assertEqual(campaign.dmp_conf, {})
        self.assertIsInstance(campaign.dmp_conf, dict)

    def test_campaign_airflow_run_id_default(self):
        """Test campaign airflow_run_id field default value."""
        campaign = CampaignFactory.create()
        self.assertEqual(campaign.airflow_run_id, 'pending')

    def test_campaign_name_max_length(self):
        """Test campaign name field max length constraint."""
        long_name = 'x' * 121  # Exceeds max_length of 120

        with self.assertRaises(Exception):  # Could be ValidationError or DataError
            CampaignFactory.create(name=long_name)

    def test_campaign_cascade_deletion_with_creator(self):
        """Test that campaign is not deleted when creator is deleted."""
        user = UserFactory.create()
        campaign = CampaignFactory.create(creator=user)
        campaign_id = campaign.id

        # Delete the user
        user.delete()

        # Campaign should still exist (DO_NOTHING means the foreign key constraint is not enforced)
        # but the creator_id field will still reference the deleted user
        self.assertTrue(Campaign.objects.filter(id=campaign_id).exists())

        # Note: With DO_NOTHING, the creator field may still reference the deleted user ID
        # This is expected behavior for DO_NOTHING cascade option


class DynamicTagModelTest(BaseTestCase):
    """Test the DynamicTag model."""

    def test_dynamic_tag_creation(self):
        """Test basic dynamic tag creation."""
        tag = DynamicTagFactory.create(
            name='Test Tag',
            description='Test description',
            tag_type='keyword',
            pattern='test',
            field='bio'
        )

        self.assertEqual(tag.name, 'Test Tag')
        self.assertEqual(tag.description, 'Test description')
        self.assertEqual(tag.tag_type, 'keyword')
        self.assertEqual(tag.pattern, 'test')
        self.assertEqual(tag.field, 'bio')
        self.assertIsNotNone(tag.id)
        self.assertIsInstance(tag.id, uuid.UUID)

    def test_dynamic_tag_str_representation(self):
        """Test dynamic tag string representation."""
        tag = DynamicTagFactory.create(name='My Test Tag')
        self.assertEqual(str(tag), 'My Test Tag')

    def test_dynamic_tag_type_choices(self):
        """Test dynamic tag type field choices."""
        valid_types = ['keyword', 'regex', 'sentiment', 'category', 'ml', 'nlp']

        for tag_type in valid_types:
            tag = DynamicTagFactory.create(tag_type=tag_type)
            self.assertEqual(tag.tag_type, tag_type)

    def test_dynamic_tag_category_relationship(self):
        """Test dynamic tag category foreign key relationship."""
        category = TagCategoryFactory.create()
        tag = DynamicTagFactory.create(category=category)

        self.assertEqual(tag.category, category)
        self.assertIn(tag, category.tags.all())

    def test_dynamic_tag_groups_relationship(self):
        """Test dynamic tag groups many-to-many relationship."""
        group1 = TagGroupFactory.create(name='Group 1')
        group2 = TagGroupFactory.create(name='Group 2')
        tag = DynamicTagFactory.create(tag_groups=[group1, group2])

        self.assertIn(group1, tag.tag_groups.all())
        self.assertIn(group2, tag.tag_groups.all())
        self.assertIn(tag, group1.tags.all())
        self.assertIn(tag, group2.tags.all())

    def test_dynamic_tag_related_tags_relationship(self):
        """Test dynamic tag related tags many-to-many relationship."""
        tag1 = DynamicTagFactory.create(name='Tag 1')
        tag2 = DynamicTagFactory.create(name='Tag 2')

        tag1.related_tags.add(tag2)

        self.assertIn(tag2, tag1.related_tags.all())
        self.assertIn(tag1, tag2.related_to.all())

    def test_dynamic_tag_is_global_default(self):
        """Test dynamic tag is_global field default value."""
        tag = DynamicTagFactory.create()
        self.assertTrue(tag.is_global)

    def test_dynamic_tag_is_system_default(self):
        """Test dynamic tag is_system field default value."""
        tag = DynamicTagFactory.create()
        self.assertFalse(tag.is_system)

    def test_dynamic_tag_weight_default(self):
        """Test dynamic tag weight field default value."""
        tag = DynamicTagFactory.create()
        self.assertEqual(tag.weight, 1.0)

    def test_system_tag_deletion_prevention(self):
        """Test that system tags cannot be deleted."""
        tag = DynamicTagFactory.create(is_system=True)

        with self.assertRaises(ValueError) as context:
            tag.delete()

        self.assertIn("Cannot delete system tag", str(context.exception))

        # Tag should still exist
        self.assertTrue(DynamicTag.objects.filter(id=tag.id).exists())

    def test_non_system_tag_deletion(self):
        """Test that non-system tags can be deleted."""
        tag = DynamicTagFactory.create(is_system=False)
        tag_id = tag.id

        tag.delete()

        # Tag should be deleted
        self.assertFalse(DynamicTag.objects.filter(id=tag_id).exists())


class TagGroupModelTest(BaseTestCase):
    """Test the TagGroup model."""

    def test_tag_group_creation(self):
        """Test basic tag group creation."""
        group = TagGroupFactory.create(
            name='Test Group',
            description='Test description',
            color='#007bff',
            is_global=True
        )

        self.assertEqual(group.name, 'Test Group')
        self.assertEqual(group.description, 'Test description')
        self.assertEqual(group.color, '#007bff')
        self.assertTrue(group.is_global)
        self.assertIsNotNone(group.id)
        self.assertIsInstance(group.id, uuid.UUID)

    def test_tag_group_str_representation(self):
        """Test tag group string representation."""
        group = TagGroupFactory.create(name='My Test Group')
        self.assertEqual(str(group), 'My Test Group')

    def test_tag_group_creator_relationship(self):
        """Test tag group creator foreign key relationship."""
        user = UserFactory.create()
        group = TagGroupFactory.create(creator=user)

        self.assertEqual(group.creator, user)

    def test_tag_group_parent_relationship(self):
        """Test tag group parent-child relationship."""
        parent_group = TagGroupFactory.create(name='Parent Group')
        child_group = TagGroupFactory.create(name='Child Group', parent=parent_group)

        self.assertEqual(child_group.parent, parent_group)
        self.assertIn(child_group, parent_group.taggroup_set.all())

    def test_tag_group_color_default(self):
        """Test tag group color field default value."""
        group = TagGroupFactory.create()
        self.assertEqual(group.color, '#6c757d')

    def test_tag_group_is_global_default(self):
        """Test tag group is_global field default value."""
        group = TagGroupFactory.create()
        self.assertTrue(group.is_global)

    def test_tag_group_priority_default(self):
        """Test tag group priority field default value."""
        group = TagGroupFactory.create()
        self.assertEqual(group.priority, 1)


class TagCategoryModelTest(BaseTestCase):
    """Test the TagCategory model."""

    def test_tag_category_creation(self):
        """Test basic tag category creation."""
        category = TagCategoryFactory.create(
            name='Test Category',
            description='Test description',
            color='#28a745',
            icon='tag',
            priority=5
        )

        self.assertEqual(category.name, 'Test Category')
        self.assertEqual(category.description, 'Test description')
        self.assertEqual(category.color, '#28a745')
        self.assertEqual(category.icon, 'tag')
        self.assertEqual(category.priority, 5)

    def test_tag_category_str_representation(self):
        """Test tag category string representation."""
        category = TagCategoryFactory.create(name='My Test Category')
        self.assertEqual(str(category), 'My Test Category')

    def test_tag_category_parent_relationship(self):
        """Test tag category parent-child relationship."""
        parent_category = TagCategoryFactory.create(name='Parent Category')
        child_category = TagCategoryFactory.create(name='Child Category', parent=parent_category)

        self.assertEqual(child_category.parent, parent_category)
        self.assertIn(child_category, parent_category.children.all())

    def test_tag_category_color_default(self):
        """Test tag category color field default value."""
        category = TagCategoryFactory.create()
        self.assertEqual(category.color, '#007bff')

    def test_tag_category_priority_default(self):
        """Test tag category priority field default value."""
        category = TagCategoryFactory.create()
        self.assertEqual(category.priority, 1)


class CampaignTagModelTest(BaseTestCase):
    """Test the CampaignTag model."""

    def test_campaign_tag_creation(self):
        """Test basic campaign tag creation."""
        campaign = CampaignFactory.create()
        tag = DynamicTagFactory.create()
        campaign_tag = CampaignTagFactory.create(
            campaign=campaign,
            tag=tag,
            is_required=True
        )

        self.assertEqual(campaign_tag.campaign, campaign)
        self.assertEqual(campaign_tag.tag, tag)
        self.assertTrue(campaign_tag.is_required)
        self.assertIsNotNone(campaign_tag.id)
        self.assertIsInstance(campaign_tag.id, uuid.UUID)

    def test_campaign_tag_str_representation(self):
        """Test campaign tag string representation."""
        campaign = CampaignFactory.create(name='Test Campaign')
        tag = DynamicTagFactory.create(name='Test Tag')
        campaign_tag = CampaignTagFactory.create(campaign=campaign, tag=tag)

        expected_str = 'Test Tag for Test Campaign'
        self.assertEqual(str(campaign_tag), expected_str)

    def test_campaign_tag_unique_together(self):
        """Test campaign tag unique together constraint."""
        campaign = CampaignFactory.create()
        tag = DynamicTagFactory.create()

        # Create first campaign tag
        CampaignTagFactory.create(campaign=campaign, tag=tag)

        # Try to create duplicate - should raise IntegrityError
        with self.assertRaises(IntegrityError):
            CampaignTagFactory.create(campaign=campaign, tag=tag)

    def test_campaign_tag_is_required_default(self):
        """Test campaign tag is_required field default value."""
        campaign_tag = CampaignTagFactory.create()
        self.assertFalse(campaign_tag.is_required)

    def test_campaign_tag_cascade_deletion(self):
        """Test cascade deletion behavior."""
        campaign = CampaignFactory.create()
        tag = DynamicTagFactory.create()
        campaign_tag = CampaignTagFactory.create(campaign=campaign, tag=tag)
        campaign_tag_id = campaign_tag.id

        # Delete campaign - should delete campaign tag
        campaign.delete()
        self.assertFalse(CampaignTag.objects.filter(id=campaign_tag_id).exists())

        # Tag should still exist
        self.assertTrue(DynamicTag.objects.filter(id=tag.id).exists())


class LocationTargetModelTest(BaseTestCase):
    """Test the LocationTarget model."""

    def test_location_target_creation(self):
        """Test basic location target creation."""
        campaign = CampaignFactory.create()
        location_target = LocationTargetFactory.create(
            campaign=campaign,
            location_id='test_location_123',
            city='Test City',
            country='Test Country'
        )

        self.assertEqual(location_target.campaign, campaign)
        self.assertEqual(location_target.location_id, 'test_location_123')
        self.assertEqual(location_target.city, 'Test City')
        self.assertEqual(location_target.country, 'Test Country')

    def test_location_target_str_representation(self):
        """Test location target string representation."""
        location_target = LocationTargetFactory.create(
            city='New York',
            country='USA'
        )
        expected_str = 'New York, USA'
        self.assertEqual(str(location_target), expected_str)

    def test_location_target_created_at_field(self):
        """Test location target created_at field is set."""
        location_target = LocationTargetFactory.create()
        self.assertIsNotNone(location_target.created_at)


class UsernameTargetModelTest(BaseTestCase):
    """Test the UsernameTarget model."""

    def test_username_target_creation(self):
        """Test basic username target creation."""
        campaign = CampaignFactory.create()
        username_target = UsernameTargetFactory.create(
            campaign=campaign,
            username='testuser123',
            audience_type='followers'
        )

        self.assertEqual(username_target.campaign, campaign)
        self.assertEqual(username_target.username, 'testuser123')
        self.assertEqual(username_target.audience_type, 'followers')
        self.assertFalse(username_target.processed)

    def test_username_target_str_representation(self):
        """Test username target string representation."""
        username_target = UsernameTargetFactory.create(username='testuser123', audience_type='followers')
        # The actual __str__ method returns "username (Audience Type Display)"
        self.assertEqual(str(username_target), 'testuser123 (Followers Only)')

    def test_username_target_audience_type_choices(self):
        """Test username target audience type field choices."""
        valid_types = ['profile', 'followers', 'following', 'both']

        for audience_type in valid_types:
            username_target = UsernameTargetFactory.create(audience_type=audience_type)
            self.assertEqual(username_target.audience_type, audience_type)

    def test_username_target_processed_default(self):
        """Test username target processed field default value."""
        username_target = UsernameTargetFactory.create()
        self.assertFalse(username_target.processed)


class ModelRelationshipTest(BaseTestCase):
    """Test model relationships and cascade behavior."""

    def test_campaign_deletion_cascades_to_targets(self):
        """Test that deleting a campaign cascades to its targets."""
        campaign = CampaignFactory.create()
        location_target = LocationTargetFactory.create(campaign=campaign)
        username_target = UsernameTargetFactory.create(campaign=campaign)

        location_target_id = location_target.id
        username_target_id = username_target.id

        # Delete campaign
        campaign.delete()

        # Targets should be deleted
        self.assertFalse(LocationTarget.objects.filter(id=location_target_id).exists())
        self.assertFalse(UsernameTarget.objects.filter(id=username_target_id).exists())

    def test_tag_group_deletion_does_not_delete_tags(self):
        """Test that deleting a tag group does not delete associated tags."""
        group = TagGroupFactory.create()
        tag = DynamicTagFactory.create(tag_groups=[group])
        tag_id = tag.id

        # Delete group
        group.delete()

        # Tag should still exist but not be associated with the group
        tag.refresh_from_db()
        self.assertTrue(DynamicTag.objects.filter(id=tag_id).exists())
        self.assertNotIn(group, tag.tag_groups.all())

    def test_tag_category_deletion_sets_null(self):
        """Test that deleting a tag category sets tag.category to null."""
        category = TagCategoryFactory.create()
        tag = DynamicTagFactory.create(category=category)

        # Delete category
        category.delete()

        # Tag should still exist but category should be None
        tag.refresh_from_db()
        self.assertIsNone(tag.category)
