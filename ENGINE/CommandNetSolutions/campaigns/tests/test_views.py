"""
Comprehensive view tests for the campaigns app.

This module tests all view functionality including:
- CRUD operations for campaigns, tags, and tag groups
- View permissions and authentication
- Form handling and validation
- Response status codes and content
- URL routing and parameter handling
- HTMX endpoints and API responses
"""

import json
import uuid
from unittest.mock import patch, MagicMock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.http import HttpResponse
from django.contrib.messages import get_messages

from campaigns.models import (
    Campaign, DynamicTag, TagGroup, TagCategory, CampaignTag,
    LocationTarget, UsernameTarget
)
from campaigns.views import CampaignDeleteView
from campaigns.tests.base import BaseTestCase, MockExternalServices
from campaigns.tests.factories import (
    CampaignFactory, DynamicTagFactory, TagGroupFactory, TagCategoryFactory,
    UserFactory, CampaignTagFactory, LocationTargetFactory, UsernameTargetFactory
)


class CampaignViewTest(BaseTestCase):
    """Test campaign-related views."""
    
    def test_campaign_list_view(self):
        """Test campaign list view."""
        # Create test campaigns
        campaign1 = CampaignFactory.create(name='Campaign 1', creator=self.user)
        campaign2 = CampaignFactory.create(name='Campaign 2', creator=self.user)
        
        url = reverse('campaigns:campaign_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Campaign 1')
        self.assertContains(response, 'Campaign 2')
        self.assertContains(response, 'campaigns')  # Check template context
    
    def test_campaign_detail_view(self):
        """Test campaign detail view."""
        campaign = CampaignFactory.create(name='Test Campaign', creator=self.user)
        
        url = reverse('campaigns:campaign_detail', kwargs={'pk': campaign.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Campaign')
        self.assertEqual(response.context['campaign'], campaign)
    
    def test_campaign_detail_view_not_found(self):
        """Test campaign detail view with non-existent campaign."""
        non_existent_id = uuid.uuid4()
        url = reverse('campaigns:campaign_detail', kwargs={'pk': non_existent_id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)
    
    def test_campaign_create_view_get(self):
        """Test campaign create view GET request."""
        url = reverse('campaigns:campaign_create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Campaign')
        self.assertIn('form', response.context)
    
    def test_campaign_create_view_post_valid(self):
        """Test campaign create view POST request with valid data."""
        url = reverse('campaigns:campaign_create')
        form_data = {
            'name': 'New Test Campaign',
            'description': 'Test description',
            'target_type': 'location',
            'audience_type': 'followers',
            'enable_location_targeting': True,
            'enable_username_targeting': False,
            'location_targets': [],
            'usernames': ''
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should redirect to campaign detail on success
        self.assertEqual(response.status_code, 302)
        
        # Check that campaign was created
        campaign = Campaign.objects.get(name='New Test Campaign')
        self.assertEqual(campaign.description, 'Test description')
        self.assertEqual(campaign.creator, self.user)
    
    def test_campaign_create_view_post_invalid(self):
        """Test campaign create view POST request with invalid data."""
        url = reverse('campaigns:campaign_create')
        form_data = {
            'name': '',  # Required field missing
            'description': 'Test description'
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should return form with errors
        self.assertEqual(response.status_code, 200)
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
    
    def test_campaign_update_view_get(self):
        """Test campaign update view GET request."""
        campaign = CampaignFactory.create(creator=self.user)
        url = reverse('campaigns:campaign_update', kwargs={'pk': campaign.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Update Campaign')
        self.assertEqual(response.context['campaign'], campaign)
    
    def test_campaign_update_view_post_valid(self):
        """Test campaign update view POST request with valid data."""
        campaign = CampaignFactory.create(name='Original Name', creator=self.user)
        url = reverse('campaigns:campaign_update', kwargs={'pk': campaign.pk})
        
        form_data = {
            'name': 'Updated Name',
            'description': 'Updated description',
            'target_type': campaign.target_type,
            'audience_type': campaign.audience_type,
            'enable_location_targeting': True,
            'enable_username_targeting': False,
            'location_targets': [],
            'usernames': ''
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should redirect to campaign detail on success
        self.assertEqual(response.status_code, 302)
        
        # Check that campaign was updated
        campaign.refresh_from_db()
        self.assertEqual(campaign.name, 'Updated Name')
        self.assertEqual(campaign.description, 'Updated description')
    
    def test_campaign_delete_view_get(self):
        """Test campaign delete view GET request."""
        campaign = CampaignFactory.create(creator=self.user)
        url = reverse('campaigns:campaign_confirm_delete', kwargs={'pk': campaign.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Delete Campaign')
        self.assertContains(response, campaign.name)
    
    @patch('campaigns.views.WhiteListEntry')
    @patch('campaigns.views.Accounts')
    @patch('campaigns.views.WorkflowExecution')
    def test_campaign_delete_view_post(self, mock_workflow, mock_accounts, mock_whitelist):
        """Test campaign delete view POST request (testing the recent form_valid fix)."""
        campaign = CampaignFactory.create(creator=self.user)
        campaign_id = campaign.id
        campaign_name = campaign.name
        
        # Mock the related models
        mock_accounts.objects.filter.return_value.count.return_value = 5
        mock_accounts.objects.filter.return_value.update.return_value = None
        mock_workflow.objects.filter.return_value.count.return_value = 2
        mock_whitelist.objects.filter.return_value.delete.return_value = None
        
        url = reverse('campaigns:campaign_confirm_delete', kwargs={'pk': campaign.pk})
        response = self.client.post(url)
        
        # Should redirect to campaign list on success
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('campaigns:campaign_list'))
        
        # Check that campaign was deleted
        self.assertFalse(Campaign.objects.filter(id=campaign_id).exists())
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any(campaign_name in str(message) for message in messages))
    
    def test_campaign_delete_view_form_valid_method_exists(self):
        """Test that CampaignDeleteView has form_valid method (recent fix)."""
        self.assertTrue(hasattr(CampaignDeleteView, 'form_valid'))
        self.assertTrue(callable(getattr(CampaignDeleteView, 'form_valid')))


class DynamicTagViewTest(BaseTestCase):
    """Test dynamic tag-related views."""
    
    def test_dynamic_tag_list_view(self):
        """Test dynamic tag list view."""
        tag1 = DynamicTagFactory.create(name='Tag 1')
        tag2 = DynamicTagFactory.create(name='Tag 2')
        
        url = reverse('campaigns:dynamic_tag_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Tag 1')
        self.assertContains(response, 'Tag 2')
    
    def test_dynamic_tag_create_view_get(self):
        """Test dynamic tag create view GET request."""
        url = reverse('campaigns:dynamic_tag_create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Tag')
        self.assertIn('form', response.context)
    
    def test_dynamic_tag_create_view_post_valid(self):
        """Test dynamic tag create view POST request with valid data."""
        category = TagCategoryFactory.create()
        url = reverse('campaigns:dynamic_tag_create')
        
        form_data = {
            'name': 'New Test Tag',
            'description': 'Test tag description',
            'category': category.id,
            'tag_type': 'keyword',
            'pattern': 'test',
            'field': 'bio',
            'is_global': True,
            'weight': 1.0
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should redirect on success
        self.assertEqual(response.status_code, 302)
        
        # Check that tag was created
        tag = DynamicTag.objects.get(name='New Test Tag')
        self.assertEqual(tag.description, 'Test tag description')
        self.assertEqual(tag.category, category)
    
    def test_dynamic_tag_update_view_get(self):
        """Test dynamic tag update view GET request."""
        tag = DynamicTagFactory.create()
        url = reverse('campaigns:dynamic_tag_update', kwargs={'pk': tag.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Update Tag')
        self.assertEqual(response.context['dynamictag'], tag)
    
    def test_dynamic_tag_delete_view_system_tag_protection(self):
        """Test that system tags cannot be deleted."""
        system_tag = DynamicTagFactory.create(is_system=True, name='System Tag')
        url = reverse('campaigns:dynamic_tag_delete', kwargs={'pk': system_tag.pk})
        
        # GET request should redirect with error message
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        
        # POST request should also redirect with error message
        response = self.client.post(url)
        self.assertEqual(response.status_code, 302)
        
        # Tag should still exist
        self.assertTrue(DynamicTag.objects.filter(id=system_tag.id).exists())
    
    def test_dynamic_tag_delete_view_non_system_tag(self):
        """Test that non-system tags can be deleted."""
        tag = DynamicTagFactory.create(is_system=False, name='Regular Tag')
        tag_id = tag.id
        url = reverse('campaigns:dynamic_tag_delete', kwargs={'pk': tag.pk})
        
        # GET request should show confirmation page
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Delete Tag')
        
        # POST request should delete the tag
        response = self.client.post(url)
        self.assertEqual(response.status_code, 302)
        
        # Tag should be deleted
        self.assertFalse(DynamicTag.objects.filter(id=tag_id).exists())


class TagGroupViewTest(BaseTestCase):
    """Test tag group-related views."""
    
    def test_tag_group_list_view(self):
        """Test tag group list view."""
        group1 = TagGroupFactory.create(name='Group 1')
        group2 = TagGroupFactory.create(name='Group 2')
        
        url = reverse('campaigns:tag_group_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Group 1')
        self.assertContains(response, 'Group 2')
    
    def test_tag_group_detail_view(self):
        """Test tag group detail view."""
        group = TagGroupFactory.create(name='Test Group')
        tag = DynamicTagFactory.create(tag_groups=[group])
        
        url = reverse('campaigns:tag_group_detail', kwargs={'pk': group.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Group')
        self.assertEqual(response.context['tag_group'], group)
    
    def test_tag_group_create_view_post_valid(self):
        """Test tag group create view POST request with valid data."""
        url = reverse('campaigns:tag_group_create')
        form_data = {
            'name': 'New Test Group',
            'description': 'Test group description',
            'color': '#007bff',
            'is_global': True
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should redirect on success
        self.assertEqual(response.status_code, 302)
        
        # Check that group was created
        group = TagGroup.objects.get(name='New Test Group')
        self.assertEqual(group.description, 'Test group description')
        self.assertEqual(group.creator, self.user)


class CampaignTagViewTest(BaseTestCase):
    """Test campaign tag-related views."""
    
    def test_campaign_tag_list_view(self):
        """Test campaign tag list view."""
        campaign = CampaignFactory.create(creator=self.user)
        tag = DynamicTagFactory.create()
        campaign_tag = CampaignTagFactory.create(campaign=campaign, tag=tag)
        
        url = reverse('campaigns:campaign_tags', kwargs={'campaign_id': campaign.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, tag.name)
        self.assertEqual(response.context['campaign'], campaign)
    
    def test_campaign_tag_create_view_post_valid(self):
        """Test campaign tag create view POST request with valid data."""
        campaign = CampaignFactory.create(creator=self.user)
        tag = DynamicTagFactory.create(is_global=True)
        
        url = reverse('campaigns:create_campaign_tag', kwargs={'campaign_id': campaign.id})
        form_data = {
            'tag': tag.id,
            'is_required': True
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should redirect on success
        self.assertEqual(response.status_code, 302)
        
        # Check that campaign tag was created
        campaign_tag = CampaignTag.objects.get(campaign=campaign, tag=tag)
        self.assertTrue(campaign_tag.is_required)
    
    def test_campaign_tag_delete_view(self):
        """Test campaign tag delete view."""
        campaign = CampaignFactory.create(creator=self.user)
        tag = DynamicTagFactory.create()
        campaign_tag = CampaignTagFactory.create(campaign=campaign, tag=tag)
        campaign_tag_id = campaign_tag.id
        
        url = reverse('campaigns:delete_campaign_tag', kwargs={'pk': campaign_tag.pk})
        response = self.client.post(url)
        
        # Should redirect on success
        self.assertEqual(response.status_code, 302)
        
        # Check that campaign tag was deleted
        self.assertFalse(CampaignTag.objects.filter(id=campaign_tag_id).exists())


class APIViewTest(BaseTestCase):
    """Test API endpoints."""
    
    def test_location_search_view(self):
        """Test location search API view."""
        url = reverse('campaigns:api_location_search')
        response = self.client.get(url, {'q': 'New York'})
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        
        # Response should be valid JSON
        data = json.loads(response.content)
        self.assertIsInstance(data, list)
    
    def test_check_tag_exists_view(self):
        """Test check tag exists API view."""
        tag = DynamicTagFactory.create(name='Existing Tag')
        
        url = reverse('campaigns:api_check_tag_exists')
        
        # Test existing tag
        response = self.client.get(url, {'name': 'Existing Tag'})
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['exists'])
        
        # Test non-existing tag
        response = self.client.get(url, {'name': 'Non-existing Tag'})
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertFalse(data['exists'])
