"""
Forms for campaign-related functionality in the campaigns app.
"""
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db import models
import json
import uuid
import os
import csv
import logging
from django.conf import settings

from campaigns.models import Campaign, LocationTarget, UsernameTarget, DynamicTag, CampaignTagRule, TagGroup, CampaignTag

logger = logging.getLogger(__name__)


class CampaignForm(forms.ModelForm):
    """
    Form for creating and editing campaigns.
    """
    # Add fields for direct target selection
    location_targets = forms.MultipleChoiceField(
        required=False,
        label="Location Targets",
        help_text="Select one or more locations to target",
        widget=forms.SelectMultiple(attrs={'class': 'location-targets-select', 'style': 'display:none;'})
    )

    location_search = forms.CharField(
        required=False,
        label="Search Locations",
        widget=forms.TextInput(attrs={
            'class': 'form-control location-search-field',
            'placeholder': 'Search for locations...',
            'autocomplete': 'off'
        })
    )

    usernames = forms.CharField(
        required=False,
        label="Usernames",
        help_text="Enter one username per line",
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control username-target-field'})
    )

    # Add toggle for target types
    enable_location_targeting = forms.BooleanField(
        required=False,
        initial=True,
        label="Enable Location Targeting",
        widget=forms.CheckboxInput(attrs={'class': 'target-toggle'})
    )

    enable_username_targeting = forms.BooleanField(
        required=False,
        initial=True,
        label="Enable Username Targeting",
        widget=forms.CheckboxInput(attrs={'class': 'target-toggle'})
    )

    class Meta:
        model = Campaign
        fields = ['name', 'description', 'target_type', 'audience_type']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make sure UUID field is not required for new campaigns
        if 'id' in self.fields:
            self.fields['id'].required = False

        # Populate location choices from the locations.csv file
        self.fields['location_targets'].choices = self._get_location_choices()

        # Hide the target_type field as we'll determine it from the toggles
        self.fields['target_type'].widget = forms.HiddenInput()

        # Set initial values for existing campaign
        if self.instance and self.instance.pk:
            # Set initial location targets
            location_ids = list(self.instance.location_targets.values_list('location_id', flat=True))
            self.fields['location_targets'].initial = location_ids

            # Set initial usernames as JSON
            username_targets = []
            for target in self.instance.username_targets.all():
                username_targets.append({
                    'username': target.username,
                    'audienceType': target.audience_type
                })
            self.fields['usernames'].initial = json.dumps(username_targets)

            # Set targeting toggles based on target type
            if self.instance.target_type == 'location':
                self.fields['enable_location_targeting'].initial = True
                self.fields['enable_username_targeting'].initial = False
            elif self.instance.target_type == 'username':
                self.fields['enable_location_targeting'].initial = False
                self.fields['enable_username_targeting'].initial = True
            else:  # mixed
                self.fields['enable_location_targeting'].initial = True
                self.fields['enable_username_targeting'].initial = True

    def _get_location_choices(self):
        """Get choices list for the location field.
        Include some default choices and load others asynchronously."""
        # Start with an empty list
        choices = []

        # Get existing location targets for this campaign if editing
        existing_location_ids = []
        if self.instance and self.instance.pk:
            existing_location_ids = list(self.instance.location_targets.values_list('location_id', flat=True))

            # Add existing location targets first
            for target in self.instance.location_targets.all():
                location_id = target.location_id
                label = f"{target.city}, {target.country} ({location_id})"
                choices.append((location_id, label))

            # Return only the existing location targets for this campaign
            return choices

        # For new campaigns, we'll just load locations from the CSV file

        # We'll only load a limited number of additional locations from the CSV file
        # to avoid performance issues and UI clutter
        try:
            locations_file = os.path.join(settings.BASE_DIR, 'campaigns', 'data', 'locations.csv')
            if os.path.exists(locations_file):
                with open(locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    count = 0
                    max_additional = 10  # Limit to 10 additional locations

                    for row in reader:
                        location_id = row.get('location_id')
                        if (location_id and
                            not any(choice[0] == location_id for choice in choices)):

                            label = f"{row.get('city', '')}, {row.get('country', '')} ({location_id})"
                            choices.append((location_id, label))
                            count += 1

                            if count >= max_additional:
                                break
        except Exception as e:
            logger.error(f"Error loading locations from CSV: {str(e)}")

        return choices

    def clean(self):
        cleaned_data = super().clean()
        location_targets = cleaned_data.get('location_targets', [])
        usernames_json = cleaned_data.get('usernames', '')
        enable_location_targeting = cleaned_data.get('enable_location_targeting', False)
        enable_username_targeting = cleaned_data.get('enable_username_targeting', False)

        # Log the location targets for debugging
        logger.info(f"Location targets in clean method (before filtering): {location_targets}")
        logger.info(f"Raw POST data for location_targets: {self.data.getlist('location_targets')}")

        # Always remove any validation errors for location_targets
        # This allows us to add new locations that aren't in the initial choices
        if 'location_targets' in self._errors:
            del self._errors['location_targets']

        # Make sure location_targets is in cleaned_data
        if 'location_targets' not in cleaned_data:
            # Try to get from raw POST data if not in cleaned_data
            raw_location_targets = self.data.getlist('location_targets')
            if raw_location_targets:
                logger.info(f"Using raw POST data for location_targets: {raw_location_targets}")
                cleaned_data['location_targets'] = raw_location_targets
            else:
                cleaned_data['location_targets'] = location_targets

        # Log the final location targets
        logger.info(f"Location targets in clean method (final): {cleaned_data['location_targets']}")

        # Check if we have any targets
        has_location_targets = bool(cleaned_data['location_targets'])
        has_username_targets = bool(usernames_json and usernames_json.strip())

        # Determine target type based on what targets are provided
        if has_location_targets and has_username_targets:
            cleaned_data['target_type'] = 'mixed'
        elif has_location_targets:
            cleaned_data['target_type'] = 'location'
        elif has_username_targets:
            cleaned_data['target_type'] = 'username'
        elif self.instance and self.instance.pk:
            # If editing an existing campaign and no targets are provided,
            # preserve the original target_type
            cleaned_data['target_type'] = self.instance.target_type
        else:
            # Default to location for new campaigns with no targets
            cleaned_data['target_type'] = 'location'

            # No targets provided - only validate if we're submitting the form
            # Don't raise an error if we're just saving a draft
            if self.instance and self.instance.pk and self.instance.status != 'draft':
                # For non-draft campaigns, we need at least one target
                raise ValidationError(_("You must add at least one location or username target."))

        # Always set both targeting flags to true for backward compatibility
        cleaned_data['enable_location_targeting'] = True
        cleaned_data['enable_username_targeting'] = True

        # Process username targets if provided
        if has_username_targets:
            # Parse usernames from JSON
            try:
                # Try to parse as JSON
                username_targets = json.loads(usernames_json)
                if not username_targets:
                    self.add_error('usernames', _("Please enter at least one valid username."))

                # Validate each username target
                for target in username_targets:
                    if not isinstance(target, dict):
                        self.add_error('usernames', _("Invalid username target format."))
                        break

                    if 'username' not in target or not target['username'].strip():
                        self.add_error('usernames', _("Username cannot be empty."))
                        break

                    if 'audienceType' not in target:
                        target['audienceType'] = 'profile'  # Default

                    # Validate audience type
                    valid_audience_types = [choice[0] for choice in Campaign.AUDIENCE_TYPE_CHOICES]
                    if target['audienceType'] not in valid_audience_types:
                        target['audienceType'] = 'profile'  # Default to profile if invalid

                cleaned_data['username_targets'] = username_targets

            except json.JSONDecodeError:
                # If not JSON, try to parse as newline-separated (for backward compatibility)
                try:
                    username_list = [u.strip() for u in usernames_json.split('\n') if u.strip()]
                    if not username_list:
                        self.add_error('usernames', _("Please enter at least one valid username."))

                    # Convert to the new format
                    username_targets = [{'username': u, 'audienceType': 'profile'} for u in username_list]
                    cleaned_data['username_targets'] = username_targets
                except Exception as e:
                    self.add_error('usernames', _("Invalid username format: {}").format(str(e)))

        return cleaned_data

    def save(self, commit=True):
        """
        Save the campaign and its targets.
        """
        # Save the campaign first
        campaign = super().save(commit=commit)

        if commit:
            # Save location targets
            location_targets = self.cleaned_data.get('location_targets', [])
            if location_targets:
                # Delete existing location targets
                campaign.location_targets.all().delete()

                # Add new location targets
                for location_id in location_targets:
                    # Try to get location details from the CSV file
                    location_data = self._get_location_data(location_id)
                    if location_data:
                        LocationTarget.objects.create(
                            id=uuid.uuid4(),
                            campaign=campaign,
                            location_id=location_id,
                            country=location_data.get('country', ''),
                            city=location_data.get('city', '')
                        )
                    else:
                        # If location details not found, create with minimal data
                        LocationTarget.objects.create(
                            id=uuid.uuid4(),
                            campaign=campaign,
                            location_id=location_id
                        )

            # Save username targets
            username_targets = self.cleaned_data.get('username_targets', [])
            if username_targets:
                # Delete existing username targets
                campaign.username_targets.all().delete()

                # Add new username targets
                for target in username_targets:
                    UsernameTarget.objects.create(
                        id=uuid.uuid4(),
                        campaign=campaign,
                        username=target['username'],
                        audience_type=target.get('audienceType', 'profile')
                    )

        return campaign

    def _get_location_data(self, location_id):
        """
        Get location data from the CSV file.
        """
        try:
            locations_file = os.path.join(settings.BASE_DIR, 'campaigns', 'data', 'locations.csv')
            if os.path.exists(locations_file):
                with open(locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        if row.get('location_id') == location_id:
                            return row
        except Exception as e:
            logger.error(f"Error getting location data: {str(e)}")
        return None


class LocationTargetForm(forms.ModelForm):
    """
    Form for adding location targets to a campaign.
    """
    class Meta:
        model = LocationTarget
        fields = ['country', 'city', 'location_id']


class UsernameTargetForm(forms.ModelForm):
    """
    Form for adding username targets to a campaign.
    """
    class Meta:
        model = UsernameTarget
        fields = ['username']


class BulkUsernameForm(forms.Form):
    """
    Form for adding multiple usernames at once.
    """
    usernames = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        help_text=_("Enter one username per line.")
    )

    def clean_usernames(self):
        data = self.cleaned_data['usernames']
        usernames = [u.strip() for u in data.split('\n') if u.strip()]

        if not usernames:
            raise ValidationError(_("Please enter at least one username."))

        return usernames


class CampaignTagForm(forms.ModelForm):
    """
    Form for assigning tags to campaigns.
    """
    class Meta:
        model = CampaignTag
        fields = ['tag', 'is_required']
        widgets = {
            'tag': forms.Select(attrs={'class': 'form-select'}),
            'is_required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        campaign = kwargs.pop('campaign', None)
        super().__init__(*args, **kwargs)

        # Filter tags to show only global tags and those not already assigned to this campaign
        if campaign:
            assigned_tag_ids = CampaignTag.objects.filter(campaign=campaign).values_list('tag_id', flat=True)

            # If we're updating an existing CampaignTag, exclude its current tag from the filter
            # so it remains available in the dropdown
            if self.instance and self.instance.pk:
                try:
                    if self.instance.tag:
                        assigned_tag_ids = assigned_tag_ids.exclude(id=self.instance.tag.id)
                except CampaignTag.tag.RelatedObjectDoesNotExist:
                    # No tag assigned yet, continue normally
                    pass

            self.fields['tag'].queryset = DynamicTag.objects.filter(
                models.Q(is_global=True) |
                models.Q(tag_groups__is_global=True)
            ).exclude(id__in=assigned_tag_ids).order_by('name')
        else:
            self.fields['tag'].queryset = DynamicTag.objects.filter(
                models.Q(is_global=True) |
                models.Q(tag_groups__is_global=True)
            ).order_by('name')
